"use client";

import React, { useEffect, useState } from "react";
import { StaticContentPage } from "@/components/StaticContentPage";
import dynamic from "next/dynamic";
import { useSearchParams } from "next/navigation";

// Dynamic imports for wallet components
const PaymentForm = dynamic(
  () =>
    import("@/components/wallet/PaymentForm").then((mod) => ({
      default: mod.PaymentForm,
    })),
  {
    loading: () => <div className="h-64 bg-gray-200 animate-pulse rounded" />,
    ssr: false,
  }
);

const WalletBalance = dynamic(
  () =>
    import("@/components/wallet/WalletBalance").then((mod) => ({
      default: mod.WalletBalance,
    })),
  {
    loading: () => <div className="h-20 bg-gray-200 animate-pulse rounded" />,
    ssr: false,
  }
);
import { useSupabase } from "@/components/SessionProvider";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle } from "lucide-react";

export default function SendPaymentPage() {
  const { session } = useSupabase();
  const searchParams = useSearchParams();
  const [prefillData, setPrefillData] = useState({
    address: "",
    amount: "",
    memo: "",
    network: "",
  });

  useEffect(() => {
    // Extract URL parameters for payment requests
    const address = searchParams.get("address") || "";
    const amount = searchParams.get("amount") || "";
    const memo = searchParams.get("memo") || "";
    const network = searchParams.get("network") || "";

    setPrefillData({ address, amount, memo, network });
  }, [searchParams]);

  return (
    <StaticContentPage title="Send Cryptocurrency Payment">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">Send Payment</h1>
          <p className="text-muted-foreground">
            Send cryptocurrency to any wallet address
          </p>
          {prefillData.network && (
            <div className="inline-flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">
              Payment request for {prefillData.network}
            </div>
          )}
        </div>

        {/* Authentication Check */}
        {!session && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Please login to access payment features.
            </AlertDescription>
          </Alert>
        )}

        {session && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Payment Form with prefilled data */}
            <PaymentForm
              recipientAddress={prefillData.address}
              amount={prefillData.amount}
              purpose={prefillData.memo}
            />

            {/* Wallet Balance */}
            <WalletBalance />
          </div>
        )}
      </div>
    </StaticContentPage>
  );
}
